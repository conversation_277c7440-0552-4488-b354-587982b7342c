@use './tailwind.css';
@import "@ionic/angular/css/core.css";
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";
@import '@ionic/angular/css/palettes/dark.system.css';
@import "primeicons/primeicons.css";
@import "@flaticon/flaticon-uicons/css/all/all.css";

ion-content {
  --background: transparent !important;
}

ion-menu {
  --width: 280px !important;
}

html {
  font-size: 80%;
}

body {
  margin: 0;
  font-size: 85%;
}

.p-accordionheader,
.p-card-body {
  padding: 12px !important;
  border: none !important;
}

.p-datatable-table {
  .p-datatable-thead {
    tr {
      th {
        font-size: 13px;
        &:first-child {
          border-top-left-radius: 6px;
        }

        &:last-child {
          border-top-right-radius: 6px;
        }
      }
    }
  }

  .p-datatable-tbody {
    tr {
      td{
        font-size: 13px;
      }
      &:last-child {
        td {
          &:first-child {
            border-bottom-left-radius: 6px;
          }

          &:last-child {
            border-bottom-right-radius: 6px;
          }
        }
      }
    }
  }
}

.billing-table {
  .p-datatable-table {
    .p-datatable-thead {
      tr {
        th {
          padding: 10px 5px !important;
        }
      }
    }

    .p-datatable-tbody {
      tr {
        td {
          padding: 5px !important;
        }
      }
    }
  }
}

// Enhanced search input with visible cursor for barcode scanning - no focus borders
.search-input-with-caret {
  caret-color: #007bff !important;

  &:focus {
    caret-color: #007bff !important;
    outline: none !important;
    border-color: inherit !important;
    box-shadow: none !important;
  }

  &::placeholder {
    color: #6c757d !important;
    opacity: 0.7 !important;
  }

  // Ensure cursor is always visible even when not focused
  &:not(:focus) {
    caret-color: #007bff !important;
  }
}

// Override PrimeNG input styles to remove focus borders but keep cursor
.p-inputtext.search-input-with-caret {
  caret-color: #007bff !important;

  &:enabled:focus {
    caret-color: #007bff !important;
    outline: none !important;
    border-color: inherit !important;
    box-shadow: none !important;
  }

  &:enabled:hover {
    border-color: inherit !important;
  }
}

.typesense-list {
  .p-autocomplete-list-container {
    max-height: 400px !important;
  }
}

.common-form {
  .common-field {
    label {
      font-size: 12px;
    }

    ion-input {
      border: 1px solid #ccc;
      border-radius: 6px;
      --padding-start: 10px;
      --padding-end: 10px;
      width: 100%;
      height: 36px;
      line-height: 36px;
      max-height: 36px;
      min-height: 36px;
      font-size: 14px;
      display: flex;
      align-items: center;
    }

    .p-inputtext {
      border: 1px solid #ccc;
      border-radius: 6px;
      padding: 10px;
      width: 100%;
      height: 36px;
      line-height: 36px;
      max-height: 36px;
      min-height: 36px;
      font-size: 13px;
    }
    p-dropdown, p-multiselect{
      display: flex;
      align-items: center;
      width: 100%;
      height: 36px;
      max-height: 36px;
      min-height: 36px;
      font-size: 13px;
    }
  }
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
