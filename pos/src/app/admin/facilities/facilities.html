<ion-content [fullscreen]="true">
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 p-3">
    <div class="col-span-1 lg:col-span-2 mt-3">
      <app-table [tableData]="facilitiesData" [tableColumns]="facilitiesColumns" [dataKey]="'id'"
        (onRowClick)="onRowClick($event)" (onActionClick)="onActionClick($event)"></app-table>
    </div>
    <div class="col-span-1 lg:col-span-1 mt-3">
      <div class="w-full bg-white p-3 rounded-lg ">
        <app-common-form #facilitiesForm [inputFields]="facilitiesFormFields"></app-common-form>
        <div class="w-full flex items-center justify-center">
          <button class="w-full mt-3 mr-1" pButton severity="danger" [outlined]="true" label="Reset"
            (click)="resetForm()"></button>
          <button [appDisableDoubleClick]="true" class="w-full mt-3 ml-1" pButton severity="info" [outlined]="true"
            label="Update Facility" (click)="createFacility()"></button>
        </div>
      </div>
    </div>
  </div>
</ion-content>