import { CommonModule } from '@angular/common';
import { Component, ViewChild } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { TableComponent } from 'src/app/components/table/table';
import { UserStoreService } from 'src/app/firebase/user-store.service';
import { CommonFormComponent } from "src/app/components/common-form/common-form.component";
import { ButtonModule } from 'primeng/button';
import { CommonService } from 'src/app/services/common.service';
import { FacilityStoreService } from 'src/app/firebase/facility-store.service';
import { FirebaseAuthService } from 'src/app/services/firebase-auth.service';
import { SharedModule } from 'src/app/shared.module';
@Component({
  selector: 'app-users',
  templateUrl: './users.html',
  imports: [
    CommonModule,
    IonicModule,
    TableComponent,
    CommonFormComponent,
    ButtonModule,
    SharedModule
  ]
})
export class UsersComponent {
  @ViewChild('usersForm') usersForm: CommonFormComponent | any;
  usersData: any[] = [];
  facilitiesData: any[] = [];
  usersFormFields: any[] = [];
  updateData: any = {};

  usersColumns: any[] = [
    { field: 'id', header: 'ID' },
    { field: 'displayName', header: 'Name' },
    { field: 'role', header: 'Role' },
    { field: 'phoneNumber', header: 'Phone Number' },
    {
      type: 'action', buttons: [
        { type: 'delete', icon: 'pi pi-trash', severity: 'danger', outlined: true, label: 'Delete', hide: false, disableDoubleClick: true }
      ], header: 'Action'
    }
  ];
  constructor(
    private userStoreService: UserStoreService,
    private facilityStoreService: FacilityStoreService,
    private commonService: CommonService,
    private firebaseAuthService: FirebaseAuthService
  ) {
  }
  ionViewDidEnter(): void {
    this.getUsers();
  }
  async getUsers() {
    this.usersData = await this.userStoreService.getAllUsers();
    this.facilitiesData = await this.facilityStoreService.getAllFacilities();
    this.usersFormFields = [
      { title: 'Name', type: 'input', name: 'displayName', model: '', required: true },
      { title: 'Phone Number', type: 'input', name: 'phoneNumber', model: null, primeInputNumber: true, required: true, inputType: 'number', min: 10, max: 10, pattern: '^[0-9]*$' },
      { title: 'Role', type: 'dropdown', name: 'role', model: '', required: true, options: [{ label: 'Admin', value: 'admin' }, { label: 'Staff', value: 'staff' }], optionLabel: 'label', optionValue: 'value' },
      {
        title: 'Facilities', type: 'dropdown', name: 'facilities', model: '', required: true, options: this.facilitiesData.map((facility: any) => {
          return { label: facility.facilityName, value: facility.id };
        }), optionLabel: 'label', optionValue: 'value'
      },
    ];
  }
  async createUser() {
    this.usersForm.getFormData().then((formData: any) => {
      const data = formData.form.value;
      if (this.updateData.isUpdate) {
        this.userStoreService.updateUser(this.updateData.data.phoneNumber, { ...this.updateData.data, ...data }).then((res: any) => {
          if (res) {
            this.resetForm();
            this.commonService.toast({ severity: 'success', summary: 'Success', detail: 'User updated successfully' });
          } else {
            this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Failed to update user' });
          }
        });
      } else {
        const formData = {
          phoneNumber: data.phoneNumber,
          displayName: data.displayName,
          role: data.role,
          facilities: data.facilities
        }
        this.userStoreService.createUser(formData).then((res: any) => {
          if (res) {
            this.resetForm();
            this.commonService.toast({ severity: 'success', summary: 'Success', detail: 'User created successfully' });
          } else {
            this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Failed to create user' });
          }
        });
      }
    });
  }
  onRowClick(event: any) {
    const userData = event.rowData;
    this.usersForm.form.setValue({
      displayName: userData.displayName,
      phoneNumber: userData.phoneNumber?.replace('+91', ''),
      role: userData.role,
      facilities: userData.facilities || []
    });
    this.usersFormFields[1].readonly = true;
    this.updateData.data = userData;
    this.updateData.isUpdate = true;
  }
  resetForm() {
    this.usersForm.form.reset();
    this.usersFormFields[1].readonly = false;
    this.updateData = {};
    this.getUsers();
  }
  async onActionClick(event: any) {
    console.log(event);
    if (event.button?.type === 'delete') {
      const userData = event.rowData;
      const currentUser = await this.firebaseAuthService.getCurrentUser();
      if (currentUser?.phoneNumber === userData.phoneNumber) {
        this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'You cannot delete yourself' });
        return;
      }
      this.userStoreService.deleteUser(userData.phoneNumber).then((res: any) => {
        if (res) {
          this.getUsers();
          this.commonService.toast({ severity: 'success', summary: 'Success', detail: 'User deleted successfully' });
        } else {
          this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Failed to delete user' });
        }
      });
    }
  }
}