<ng-content></ng-content>
<form #myForm="ngForm" [class]="mainClass" class="common-form" autocomplete="off">
  <div class="row m-0" [ngClass]="designClass">
    <ng-container *ngFor="let inputType of inputFields; let i = index">
      <div [class]="inputType.styleClass ? inputType.styleClass : classList || ''"
        *ngIf="!inputType.hide && (inputType.type || inputType.alterType)" [hidden]="inputType.hidden">
        <ng-container *ngIf="inputType.type === 'header'">
          <h6 class="m-0">{{inputType.title}}</h6>
        </ng-container>

        <div class="common-field" [ngStyle]="inputType?.fieldStyles || {}" [ngClass]="inputType?.fieldClass || ''"
          *ngIf="inputType.type !== 'header'">
          <div class="flex justify-content-between"
            *ngIf="inputType.type !== 'checkbox' || inputType.alterType !== 'checkbox'">
            <label *ngIf="!inputType.requiredCheck && !inputType?.hideLabel"
              [class]="'mb-1 single-line ' + (inputType?.labelClass || '')">{{inputType.title}} <span
                *ngIf="inputType.required && inputType.title">*</span></label>
          </div>

          <div [ngClass]="{'d-flex align-items-center': inputType?.scanner}" class="w-full">
            <div class="w-full field-item d-flex align-items-center" *ngIf="inputType.type">
              <textarea *ngIf="inputType.type === 'textarea' || inputType.alterType === 'textarea'" class="w-full"
                [placeholder]="'Select ' + inputType.title" [readonly]="inputType.readonly"
                (keyup)="checkChange($event, inputType, i)" [ngClass]="{'readonly-input': inputType.readonly}"
                [required]="inputType.required" [name]="inputType.name ? inputType.name : inputType.type + i"
                [id]="inputType.name ? inputType.name : inputType.type + i" [rows]="inputType.rows || '2'"
                pInputTextarea [(ngModel)]="inputType.model"></textarea>

              <p-dropdown [optionLabel]="inputType.optionLabel || 'label'" class="w-full"
                [optionValue]="inputType.optionValue || 'value'" [required]="inputType.required"
                (onChange)="checkChange($event, inputType, i)"
                [name]="inputType.name ? inputType.name : inputType.type + i"
                [id]="inputType.name ? inputType.name : inputType.type + i"
                [filter]="inputType.filter ? inputType.filter : false" panelStyleClass="common-dropdown-body"
                [readonly]="inputType.readonly"
                [appendTo]="inputType.appendTo ? inputType.appendTo : appendTo ? appendTo : ''"
                [placeholder]="inputType?.placeholder || ('Select ' + inputType.title)"
                *ngIf="inputType.type === 'dropdown' || inputType.alterType === 'dropdown'"
                [(ngModel)]="inputType.model" [options]="inputType.options">
              </p-dropdown>

              <p-multiSelect class="w-full" [required]="inputType.required"
                (onChange)="checkChange($event, inputType, i)"
                [name]="inputType.name ? inputType.name : inputType.type + i"
                [id]="inputType.name ? inputType.name : inputType.type + i" panelStyleClass="common-dropdown-body"
                [readonly]="inputType.readonly"
                *ngIf="inputType.type === 'multiselect' || inputType.alterType === 'multiselect'" [showClear]="false"
                [options]="inputType.options" [(ngModel)]="inputType.model" placeholder="Select"
                [appendTo]="inputType.appendTo ? inputType.appendTo : appendTo ? appendTo : ''"
                [optionLabel]="inputType.optionLabel || 'label'" [optionValue]="inputType.optionValue || 'value'"
                [filter]="inputType.filter ?? true">
              </p-multiSelect>

              <div class="p-inputgroup config-input relative d-flex align-items-center"
                *ngIf="(inputType.type === 'input' || inputType.alterType === 'input') && inputType.inputType !== 'number'">
                <input pInputText #inputComponent [enterKeyHint]="inputType.enterKeyHint" #inputField placement="left"
                  [required]="inputType.required" [name]="inputType.name ? inputType.name : inputType.type + i"
                  [class]="inputType?.customClass || ''"
                  [id]="inputType?.idPrefix ? ((inputType?.idPrefix || '') + itemId) : (inputType?.id || inputType.name || (inputType.type + i))"
                  [readonly]="inputType.readonly" [type]="inputType.inputType ? inputType.inputType : 'text'"
                  [placeholder]="inputType.readonly ? inputType.title : (inputType?.placeholder || 'Enter ' + inputType.title)"
                  [(ngModel)]="inputType.model" (keyup)="checkChange($event, inputType, i, inputComponent)"
                  [ngClass]="{'readonly-input': inputType.readonly,'ng-invalid':inputType.invalid}"
                  [max]="inputType.max"/>

                <button [ngStyle]="inputType.iconStyles" pButton [icon]="inputType.icon" *ngIf="inputType.icon"
                  class="input-icon absolute cursor-pointer"
                  (click)="iconClick(inputType, null, inputComponent)"></button>
              </div>

              <div class="p-inputgroup config-input relative"
                *ngIf="(inputType.type === 'input' || inputType.alterType === 'input') && inputType.inputType === 'number'">
                <p-inputNumber class="w-full" #inputField *ngIf="inputType.primeInputNumber" placement="left"
                  [required]="inputType.required" [name]="inputType.name ? inputType.name : inputType.type + i"
                  [inputId]="inputType.name ? inputType.name : inputType.type + i" [allowEmpty]="true"
                  [readonly]="inputType.readonly" [minFractionDigits]="(inputType.minFractionDigits || null)"
                  [maxFractionDigits]="(inputType.maxFractionDigits || null)"
                  [placeholder]="inputType.readonly ? inputType.title : 'Enter ' + inputType.title"
                  [(ngModel)]="inputType.model" [useGrouping]="false"
                  [minlength]="inputType.min"
                  [maxlength]="inputType.max"
                  (onEvent)="checkDebounceChange($event, inputType, i)" (keyup)="checkChange($event, inputType, i)"
                  [ngClass]="{'readonly-input': inputType.readonly}">
                </p-inputNumber>

                <input pInputText #inputField *ngIf="!inputType.primeInputNumber" placement="left"
                  [required]="inputType.required" [name]="inputType.name ? inputType.name : inputType.type + i"
                  [id]="inputType?.id || (inputType.name ? inputType.name : inputType.type + i)"
                  [readonly]="inputType.readonly" type="number"
                  [placeholder]="inputType.readonly ? inputType.title : 'Enter ' + inputType.title"
                  [(ngModel)]="inputType.model" appDisableNumberInputScroll (keyup)="checkChange($event, inputType, i)"
                  [ngClass]="{'readonly-input': inputType.readonly}"/>

                <button pButton [icon]="inputType.icon" *ngIf="inputType.icon"
                  class="input-icon absolute cursor-pointer" (click)="iconClick(inputType)"></button>
              </div>

              <div class="flex justify-content-between"
                *ngIf="inputType.type === 'toggle' || inputType.alterType === 'toggle'">
                <p class="mb-0">{{inputType.title}}</p>
                <p-inputSwitch [name]="inputType.name ? inputType.name : inputType.type + i"
                  [id]="inputType.name ? inputType.name : inputType.type + i" [(ngModel)]="inputType.model"
                  class="p-mt-1">
                </p-inputSwitch>
              </div>

              <p-calendar class="w-full" panelStyleClass="calender-body-overlay" [required]="inputType.required"
                [name]="inputType.name ? inputType.name : inputType.type + i"
                [inputId]="inputType.name ? inputType.name : inputType.type + i" appendTo="body"
                [placeholder]="'Select ' + inputType.title"
                *ngIf="inputType.type === 'date' || inputType.alterType === 'date'"
                [timeOnly]="inputType.timeOnly ? inputType.timeOnly : false" [showIcon]="true"
                [dateFormat]="inputType.dateFormat || 'dd/mm/yy'" (onSelect)="checkChange($event, inputType, i)"
                [minDate]="inputType.minDate" [maxDate]="inputType.maxDate" [(ngModel)]="inputType.model"
                [dataType]="inputType.dataType || 'date'" [touchUI]="true" [showTime]="inputType.time" hourFormat="12"
                [readonlyInput]="true" [disabled]="inputType.disabled">
              </p-calendar>

              <div *ngIf="inputType.type === 'radio' || inputType.alterType === 'radio'" class="w-full"
                style="display: ruby;">
                <span class="d-flex align-items-center w-auto mr-2 mb-2"
                  *ngFor="let option of inputType.options; let k = index">
                  <p-radioButton [required]="inputType.required"
                    [name]="inputType.name ? inputType.name : inputType.type + i"
                    [id]="inputType.name ? inputType.name : inputType.type + i" [value]="option.value || option"
                    [(ngModel)]="inputType.model" [inputId]="inputType.name + k + formIndex"
                    (onClick)="checkChange($event, inputType, i)"></p-radioButton>
                  <label [for]="inputType.name + k + formIndex"
                    class="ml-1 mb-0 cursor-pointer p-0 f-s-13">{{option.label || option}}</label>
                </span>
              </div>

              <p-autoComplete [required]="inputType.required" class="w-full"
                [name]="inputType.name ? inputType.name : inputType.type + i"
                [inputId]="inputType.name ? inputType.name : inputType.type + i"
                *ngIf="inputType.type === 'auto_complete' || inputType.alterType === 'auto_complete'"
                [(ngModel)]="inputType.model" (completeMethod)="checkChange($event, inputType, i)"
                [suggestions]="inputType.suggestions"
                [placeholder]="inputType?.placeholder || ('Select ' + inputType.title)" [readonly]="inputType.readonly"
                [appendTo]="inputType.appendTo ? inputType.appendTo : appendTo ? appendTo : ''"
                [field]="inputType.fieldType ? inputType.fieldType : ''" (onSelect)="selected($event, inputType, i)">
              </p-autoComplete>
              <ng-container *ngIf="inputType.columBtn?.length > 0">
                <ng-container *ngFor="let btn of inputType.columBtn; let bi = index">
                  <button *ngIf="((inputType[btn.type+'_show'] ?? !btn.hide) && !btn.src)"
                    class="d-flex common-fields-btn p-0 flex align-items-center">
                    <i (click)="iconClick(inputType, btn)" class="m-0 ml-2 cursor-pointer flex"
                      [class]="btn.icon"></i>
                  </button>
                  <ion-img class="scanner ml-2 d-flex" *ngIf="((inputType[btn.type+'_show'] ?? !btn.hide) && btn.src)"
                    [src]="btn.src" (click)="iconClick(inputType, btn)"></ion-img>
                </ng-container>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
  </div>
</form>