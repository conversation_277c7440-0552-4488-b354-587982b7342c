import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InputNumberModule } from 'primeng/inputnumber';
import { DialogModule } from 'primeng/dialog';
import { RadioButtonModule } from 'primeng/radiobutton';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { FloatLabelModule } from 'primeng/floatlabel';

import { PaymentService, PaymentMethod, PaymentData, PaymentModalOutput } from '../../services/payment.config.service';

@Component({
  selector: 'app-payment-modal',
  standalone: true,
  templateUrl: './payment-modal.html',
  imports: [
    CommonModule, FormsModule, InputNumberModule, DialogModule,
    RadioButtonModule, ButtonModule, InputTextModule, FloatLabelModule
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PaymentModalComponent implements OnChanges {
  @Input() visible = false;
  @Input() totalAmount = 0;
  @Input() isProcessing = false;
  @Input() title = 'Complete Payment';
  @Input() confirmButtonLabel = 'Confirm Payment';
  @Input() cancelButtonLabel = 'Cancel';

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() cancel = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<PaymentModalOutput>();

  paymentData: PaymentData = { selectedPaymentMethod: 'cash' };
  remainingAmount = 0;
  change = 0;
  customerName = '';
  customerPhone = '';
  paymentMethods: PaymentMethod[] = [];

  constructor(private paymentService: PaymentService) {}

  ngOnChanges(): void {
    if (this.totalAmount !== undefined) {
      this.updateAmounts();
      this.paymentMethods = this.paymentService.getPaymentMethods(this.totalAmount);
    }
    if (this.visible) this.resetPaymentData();
  }

  trackByMethod(_: number, m: PaymentMethod): string { return m.value; }

  getSelectedMethod(): PaymentMethod | undefined {
    return this.paymentMethods.find(m => m.value === this.paymentData.selectedPaymentMethod);
  }

  private resetPaymentData(): void {
    this.paymentData = this.paymentService.initializePaymentData(this.totalAmount);
    this.customerName = '';
    this.customerPhone = '';
    this.updateAmounts();
  }

  private updateAmounts(): void {
    const amounts = this.paymentService.calculateAmounts(this.paymentData, this.totalAmount);
    this.remainingAmount = amounts.remaining;
    this.change = amounts.change;
  }

  handlePaymentMethodChange(method: string): void {
    if (!this.isProcessing) {
      this.paymentData.selectedPaymentMethod = method;
      this.updateAmounts();
    }
  }

  onAmountInput(event: number, fieldKey: string): void {
    this.paymentData[fieldKey] = event;
    this.updateAmounts();
  }

  onConfirm(): void {
    if (this.isProcessing) return;

    if (this.paymentService.validatePayment(this.paymentData, this.customerName, this.customerPhone, this.totalAmount)) {
      this.confirm.emit({
        paymentMethod: this.paymentData.selectedPaymentMethod,
        paymentData: this.paymentData,
        customerName: this.customerName,
        customerPhone: this.customerPhone
      });
    }
  }

  onCancel(): void {
    if (!this.isProcessing) {
      this.visible = false;
      this.visibleChange.emit(false);
      this.cancel.emit();
    }
  }
}