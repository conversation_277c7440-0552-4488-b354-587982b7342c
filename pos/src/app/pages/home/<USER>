<ion-content [fullscreen]="true">
  <div class="w-full bg-blue-50 p-3 rounded-md mb-3 lg:hidden" *ngIf="billingTabs[currentTab].items.length > 0">
    <div class="flex items-center justify-center gap-2">
      <span class="font-semibold">Billing Total:</span>
      <span class="text-lg font-bold">₹{{billingGrandTotal().toFixed(2)}}</span>
    </div>
  </div>
  <div class="grid grid-cols-1  lg:grid-cols-3 gap-4 p-4 w-full h-full overflow-hidden">
    <div class="col-span-1 lg:col-span-2 overflow-hidden">
      <div class="w-full mb-2">
        <p-iftalabel>
          <p-autoComplete #searchInput [(ngModel)]="searchText" [suggestions]="searchSuggestions"
            (completeMethod)="onSearch($event)" (keydown.enter)="onEnterKey($event)" (onSelect)="onSearchSelect($event)" placeholder="Search products or scan barcode..."
            [dropdown]="false" [multiple]="false" [forceSelection]="false" [autoHighlight]="true" [autofocus]="true"
            styleClass="w-full" inputStyleClass="w-full" panelStyleClass="typesense-list" [style]="{'width': '100%'}" inputStyleClass="w-full h-[4rem]">
            <ng-template let-product pTemplate="item">
              <div class="flex items-center gap-3 p-2">
                <img [src]="product.thumbnail_image || 'https://www.shorekids.co.nz/wp-content/uploads/2014/08/image-placeholder.jpg'" [alt]="product.name" class="w-10 h-10 rounded object-cover" />
                <div class=" flex flex-row gap-4 leading-none">
                  <div class="font-medium text-sm leading-none">{{product.name}}</div>
                  <div class="flex items-center gap-2 leading-none">
                    <span class="text-gray-500 text-sm leading-none"> SKU: {{product.child_sku}}</span> 
                    <span class="text-orange-600 text-sm leading-none">Price: ₹{{product.selling_price}}</span>
                    <span class="text-orange-600 text-sm leading-none" *ngIf="product.ean_number">EAN: {{product.ean_number}}</span>
                  </div>
                </div>
              </div>
            </ng-template>
          </p-autoComplete>
          <label>Search Item</label>
        </p-iftalabel>
      </div>
      <p-tabs value="{{currentTab}}" (valueChange)="onTabChange($event)">
        <p-tablist>
            <ng-container *ngFor="let tab of billingTabs; let i = index">
              <p-tab value="{{i}}">
                <span>{{tab.title}} {{i + 1}}</span>
                <i *ngIf="billingTabs.length > 1" class="pi pi-times-circle text-red-300 cursor-pointer" (click)="removeTab(i)"></i>
              </p-tab>
            </ng-container>
            <span class="flex items-center px-3"><i class="pi pi-plus text-green-500 cursor-pointer" (click)="addTab()"></i></span>
        </p-tablist>
        <p-tabpanels>
          <ng-container *ngFor="let tab of billingTabs; let i = index">
            <p-tabpanel value="{{i}}">
              <app-table [pagination]="false"  [tableData]="billingTabs[i].items" [tableColumns]="productsColumns" [dataKey]="'child_sku'" 
              [scrollable]="true" [scrollHeight]="'calc(100vh - 260px)'" (onChange)="onChange($event)" (onActionClick)="onActionClick($event)">
            </app-table>
          </p-tabpanel>
          </ng-container>
        </p-tabpanels>
    </p-tabs>
    </div>
    <div class="col-span-1 lg:col-span-1 p-4 overflow-hidden">
      <app-billing [showActions]="true" [quantityEdit]="true" [cartItems]="billingTabs[currentTab].items" (cartChange)="onCartChange($event)"></app-billing>
    </div>
  </div>
</ion-content>